# Ever Works Backend APIs

Built with NestJS.

## How to run

### 1. Clone the repository

Make sure you have [pnpm](https://pnpm.io/) installed, then clone the repository:

```sh
git clone https://github.com/ever-works/ever-works.git
```

### 2. Create `.env` file

Navigate to the `apps/api` directory and create a `.env` file. You can use the example file as a starting point:

```shell
cp .env.example .env
```

### 3. Run application using (cd to root of the whole repo, not backend app):

```sh
pnpm dev
```

### 4. Create a directory object

To create a new directory object, send a POST request to `http://localhost:3001/directories` with the following JSON body:

```json
{
    "slug": "awesome-time-tracking",
    "name": "Awesome Time Tracking",
    "description": "Time Tracking - Software, Methodologies and Practices."
}
```

By default, the directory will be created with the currently authenticated GitHub user as the owner.

If you want to initialize the directory within an organization, provide the optional `owner` field:

```json
{
    "slug": "awesome-time-tracking",
    "owner": "ever-works",
    "name": "Awesome Time Tracking",
    "description": "Time Tracking - Software, Methodologies and Practices."
}
```

### 5. Generate data and GitHub repositories

To generate data and create a GitHub repository for the directory, send a POST request to `http://localhost:3001/generate` with the following JSON body:

**Basic Request:**

```json
{
    "slug": "awesome-time-tracking",
    "name": "Awesome Time Tracking",
    "prompt": "Generate list of best time tracking software"
}
```

**Advanced Request with All Options:**

```json
{
    "slug": "awesome-time-tracking",
    "name": "Awesome Time Tracking",
    "prompt": "Generate list of best time tracking software for business. Start with Open Source projects first, then prioritize Commercial solutions. Include both open-source and commercial solutions. You can check these URLs for reference: https://github.com/awesome-lists/awesome-time-tracking https://alternativeto.net/category/productivity/time-tracking/",
    "company": {
        "name": "Acme Corporation",
        "website": "https://acme.com"
    },
    "target_keywords": [
        "time tracking",
        "productivity",
        "project management",
        "timesheet",
        "work hours"
    ],
    "initial_categories": ["Open Source", "Commercial"],
    "priority_categories": ["Enterprise", "SaaS"],
    "source_urls": [
        "https://github.com/awesome-lists/awesome-time-tracking",
        "https://alternativeto.net/category/productivity/time-tracking/"
    ],
    "generation_method": "create-update",
    "update_with_pull_request": true,
    "badge_evaluation_enabled": false,
    "website_repository_creation_method": "duplicate",
    "repository_description": "A curated list of the best time tracking software and tools for businesses.",
    "config": {
        "max_search_queries": 15,
        "max_results_per_query": 25,
        "max_pages_to_process": 150,
        "relevance_threshold_content": 0.8,
        "min_content_length_for_extraction": 300,
        "prompt_comparison_confidence_threshold": 0.6,
        "ai_first_generation_enabled": true
    }
}
```

**Request Parameters:**

| Field                                | Type     | Required   | Default         | Description                                                                                               |
| ------------------------------------ | -------- | ---------- | --------------- | --------------------------------------------------------------------------------------------------------- |
| `slug`                               | string   | `required` | -               | Unique identifier for the directory                                                                       |
| `name`                               | string   | `required` | -               | Display name for the directory                                                                            |
| `prompt`                             | string   | `required` | -               | Description/prompt for item generation. URLs mentioned here will be automatically extracted and processed |
| `company`                            | object   | `optional` | -               | Company information (see Company Object below)                                                            |
| `repository_description`             | string   | `optional` | -               | Description for the generated github repository                                                           |
| `target_keywords`                    | string[] | `optional` | `[]`            | Keywords to focus the search and generation                                                               |
| `initial_categories`                 | string[] | `optional` | `[]`            | Initial categories to assign to generated items                                                           |
| `priority_categories`                | string[] | `optional` | `[]`            | Categories that should appear first in the final output (can also be extracted from prompt)               |
| `source_urls`                        | string[] | `optional` | `[]`            | Additional URLs to process for content extraction                                                         |
| `generation_method`                  | enum     | `optional` | `create-update` | Generation method: `create-update` or `recreate` (see Generation Methods below)                           |
| `update_with_pull_request`           | boolean  | `optional` | `true`          | Whether to update the repository with a pull request or directly commit the changes to main branch.       |
| `website_repository_creation_method` | enum     | `optional` | `duplicate`     | Method for creating the website repository: `duplicate`, `fork`, or `create-using-template` (see below)   |
| `badge_evaluation_enabled`           | boolean  | `optional` | `false`         | Whether to evaluate badges for the generated items                                                        |
| `config`                             | object   | `optional` | -               | Advanced configuration options                                                                            |

**Company Object:**

| Field     | Type   | Required                           | Description                                                                           |
| --------- | ------ | ---------------------------------- | ------------------------------------------------------------------------------------- |
| `name`    | string | `required` (when company provided) | Company name that will be written to config.yml                                       |
| `website` | string | `required` (when company provided) | Company website URL (must be valid HTTP/HTTPS URL) that will be written to config.yml |

**Configuration Options:**

| Field                                    | Type    | Default | Range    | Description                                                                                       |
| ---------------------------------------- | ------- | ------- | -------- | ------------------------------------------------------------------------------------------------- |
| `max_search_queries`                     | number  | 10      | 1-100    | Maximum number of search queries to execute                                                       |
| `max_results_per_query`                  | number  | 20      | 1-100    | Maximum results to process per search query                                                       |
| `max_pages_to_process`                   | number  | 100     | 1-1000   | Maximum web pages to process for content extraction                                               |
| `relevance_threshold_content`            | number  | 0.75    | 0.01-1.0 | Minimum relevance score for content filtering                                                     |
| `min_content_length_for_extraction`      | number  | 300     | 0+       | Minimum content length required for item extraction                                               |
| `ai_first_generation_enabled`            | boolean | true    | -        | Enable AI-first item generation before web search                                                 |
| `prompt_comparison_confidence_threshold` | number  | 0.5     | 0.01-1.0 | Minimum confidence score for prompt comparison (used when `generation_method` is `create-update`) |

**Generation Methods:**

| Generation Method | Description                                                                                                                                                                                          |
| ----------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `create-update`   | **Default behavior.** Creates a new repository if it doesn't exist, or updates an existing repository by adding new items. Existing items are preserved and new items are deduplicated against them. |
| `recreate`        | **Complete rebuild.** Entirely recreates the repository with fresh data, removing all existing content and replacing it with newly generated items.                                                  |

**Website Repository Creation Methods:**

| Method                  | Description                                                                                                                                 |
| ----------------------- | ------------------------------------------------------------------------------------------------------------------------------------------- |
| `duplicate`             | **Default behavior.** Creates an independent copy (duplicate) of the template repository. This is a full clone.                             |
| `fork`                  | Creates a fork of the template repository under the specified user or organization. This maintains a link to the original template.         |
| `create-using-template` | Creates a new repository using the template repository as a GitHub template. This initializes the new repository with the template's files. |

**Features:**

- **URL Extraction**: URLs mentioned in the prompt are automatically extracted and processed
- **AI-Powered Generation**: Initial items generated using AI before web search
- **Intelligent Search**: Multiple search queries generated from keywords and description
- **Content Filtering**: Relevance assessment and content quality filtering
- **Deduplication**: Advanced deduplication using both field-based and AI-based methods
- **Categorization**: Automatic category and tag generation with consistency across batches
- **Priority Categories**: Categories can be prioritized to appear first in the final output
- **Smart Category Extraction**: Priority categories can be extracted from natural language prompts
- **Source Validation**: URL validation and fallback search for invalid sources
- **Badge Evaluation**: Optional badge evaluation for generated items
- **Batch Processing**: Efficient processing with rate limiting and parallel execution
- **Markdown Generation**: Detailed markdown summaries for each item (available via separate endpoint)

> This is a long-running task that may take 5-15 minutes depending on the configuration and number of items processed. The system uses intelligent batching and rate limiting to ensure reliable processing.

### 6. Update Directory

This streamlines the process of updating an existing directory without requiring a request body, in contrast to the behavior of the `/generate` endpoint.

**Endpoint:**

```
POST /update/{slug}
```

**Request Body (Optional):**

```json
{
    "generation_method": "create-update",
    "update_with_pull_request": true
}
```

**Response:**

```json
{
    "status": "pending",
    "slug": "awesome-time-tracking",
    "parameters": {...},
    "message": "Directory 'awesome-time-tracking' is being updated. Check back later"
}
```

**URL Parameters:**

| Parameter | Type   | Required   | Description                         |
| --------- | ------ | ---------- | ----------------------------------- |
| `slug`    | string | `required` | The slug of the directory to update |

**POST Request Body Parameters:**

| Field                      | Type    | Required   | Description                                                                                                           |
| -------------------------- | ------- | ---------- | --------------------------------------------------------------------------------------------------------------------- |
| `generation_method`        | enum    | `optional` | Generation method: `create-update` or `recreate` (default: `create-update`)                                           |
| `update_with_pull_request` | boolean | `optional` | Whether to update the repository with a pull request or directly commit the changes to main branch. (default: `true`) |

### 6. Submit Individual Items

To submit individual items to an existing directory, send a POST request to `http://localhost:3001/submit-item/{slug}` with the item details.

**Endpoint:**

```
POST /submit-item/{slug}
```

**URL Parameters:**

| Parameter | Type   | Required   | Description                                     |
| --------- | ------ | ---------- | ----------------------------------------------- |
| `slug`    | string | `required` | The slug of the directory to submit the item to |

**Request Body:**

```json
{
    "name": "Awesome Tool",
    "description": "A really useful development tool",
    "source_url": "https://github.com/example/awesome-tool",
    "category": "Development Tools",
    "tags": ["productivity", "open-source"],
    "featured": false,
    "pay_and_publish_now": false
}
```

**Request Parameters:**

| Field                 | Type     | Required   | Description                                                         |
| --------------------- | -------- | ---------- | ------------------------------------------------------------------- |
| `name`                | string   | `required` | Item name                                                           |
| `description`         | string   | `required` | Item description                                                    |
| `source_url`          | string   | `required` | Valid HTTP/HTTPS URL for the item                                   |
| `category`            | string   | `required` | Category name for the item                                          |
| `tags`                | string[] | `optional` | Array of tag strings                                                |
| `featured`            | boolean  | `optional` | Whether item should be featured (default: false)                    |
| `pay_and_publish_now` | boolean  | `optional` | Force auto-merge regardless of config (default: false)              |
| `slug`                | string   | `optional` | Custom slug for the item (auto-generated from name if not provided) |

**Response:**

```json
{
    "status": "success",
    "slug": "awesome-time-tracking",
    "item_name": "Awesome Tool",
    "message": "Item \"Awesome Tool\" has been submitted for review. PR #42 created.",
    "pr_number": 42,
    "pr_url": "https://github.com/owner/repo-data/pull/42",
    "branch_name": "feature-1640995200000-abc123",
    "auto_merged": false
}
```

**Response Fields:**

| Field           | Type    | Description                                                     |
| --------------- | ------- | --------------------------------------------------------------- |
| `status`        | string  | Status of the operation: `success`, `error`, or `pending`       |
| `slug`          | string  | Directory slug                                                  |
| `item_name`     | string  | Name of the submitted item                                      |
| `message`       | string  | Status message                                                  |
| `pr_number`     | number  | _(Success only)_ GitHub PR number if created                    |
| `pr_url`        | string  | _(Success only)_ GitHub PR URL if created                       |
| `branch_name`   | string  | _(Success only)_ Git branch name if created                     |
| `auto_merged`   | boolean | _(Success only)_ Whether the PR was automatically merged        |
| `error_details` | string  | _(Error only)_ Additional details about the error that occurred |

**Auto-Merge Behavior:**

The PR will be automatically merged if either:

1. `pay_and_publish_now` is set to `true` in the request
2. `autoapproval` is set to `true` in the repository's config.yml

Otherwise, the PR will be created and require manual review.

**Example with Immediate Publishing:**

```bash
curl -X POST http://localhost:3001/submit-item/awesome-time-tracking \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Premium Tool",
    "description": "A premium development tool",
    "source_url": "https://example.com/premium-tool",
    "category": "Development Tools",
    "tags": ["premium", "enterprise"],
    "featured": true,
    "pay_and_publish_now": true
  }'
```

**Process Flow:**

1. **Validation**: Request body is validated
2. **Repository Access**: Data repository is cloned/pulled
3. **Config Check**: Repository config is checked for autoapproval settings
4. **Branch Creation**: New feature branch is created
5. **Content Generation**: AI generates markdown content for the item
6. **File Creation**: Item YAML and markdown files are created
7. **Commit & Push**: Changes are committed and pushed
8. **PR Creation**: Pull request is created
9. **Auto-Merge** (conditional): PR is merged if auto-merge conditions are met

### 7. Remove Individual Items

To remove individual items from an existing directory, send a POST request to `http://localhost:3001/remove-item/{slug}` with the item details.

**Endpoint:**

```
POST /remove-item/{slug}
```

**URL Parameters:**

| Parameter | Type   | Required   | Description                                       |
| --------- | ------ | ---------- | ------------------------------------------------- |
| `slug`    | string | `required` | The slug of the directory to remove the item from |

**Request Body:**

```json
{
    "item_slug": "awesome-tool",
    "reason": "Item is no longer maintained",
    "pay_and_publish_now": false
}
```

**Request Parameters:**

| Field                 | Type    | Required   | Description                                                       |
| --------------------- | ------- | ---------- | ----------------------------------------------------------------- |
| `item_slug`           | string  | `required` | The slug of the item to remove                                    |
| `reason`              | string  | `optional` | Reason for removing the item (will be included in commit message) |
| `pay_and_publish_now` | boolean | `optional` | Force auto-merge regardless of config (default: false)            |

**Response:**

```json
{
    "status": "success",
    "slug": "awesome-time-tracking",
    "item_name": "Awesome Tool",
    "item_slug": "awesome-tool",
    "message": "Item \"Awesome Tool\" removal has been submitted for review. PR #43 created.",
    "pr_number": 43,
    "pr_url": "https://github.com/owner/repo-data/pull/43",
    "branch_name": "feature-1640995200000-def456",
    "auto_merged": false
}
```

**Response Fields:**

| Field           | Type   | Description                                                     |
| --------------- | ------ | --------------------------------------------------------------- |
| `status`        | string | Status of the operation: `success`, `error`, or `pending`       |
| `slug`          | string | Directory slug                                                  |
| `item_name`     | string | Name of the removed item                                        |
| `item_slug`     | string | Slug of the removed item                                        |
| `message`       | string | Status message                                                  |
| `pr_number`     | number | _(Success only)_ GitHub PR number if created                    |
| `pr_url`        | string | _(Success only)_ GitHub PR URL if created                       |
| `branch_name`   | string | _(Success only)_ Git branch name if created                     |
| `error_details` | string | _(Error only)_ Additional details about the error that occurred |

**Example with Immediate Publishing:**

```bash
curl -X POST http://localhost:3001/remove-item/awesome-time-tracking \
  -H "Content-Type: application/json" \
  -d '{
    "item_slug": "outdated-tool",
    "reason": "Tool is no longer maintained and has security vulnerabilities"
  }'
```

### 8. Update website repository

To update an existing website repository with the latest changes from the template repository, send a POST request to `http://localhost:3001/update-website/{slug}`.
the `slug` parameter should match the directory slug used when creating the website repository.

This endpoint updates an existing website repository by pulling the latest changes from the template repository. It automatically detects the original creation method and applies the appropriate update strategy.

**Request:**

```
POST /update-website/awesome-time-tracking
```

**URL Parameters:**

| Parameter | Type   | Required   | Description                                    |
| --------- | ------ | ---------- | ---------------------------------------------- |
| `slug`    | string | `required` | The slug of the directory/repository to update |

**Response:**

```json
{
    "status": "success",
    "slug": "awesome-time-tracking",
    "owner": "ever-works",
    "repository": "ever-works/awesome-time-tracking-website",
    "message": "Successfully updated using duplicate method",
    "method_used": "duplicate"
}
```

**Response Fields:**

| Field           | Type   | Description                                                               |
| --------------- | ------ | ------------------------------------------------------------------------- |
| `status`        | string | Status of the operation: `success` or `error`                             |
| `slug`          | string | The directory slug that was updated                                       |
| `owner`         | string | The GitHub owner (user or organization) of the repository                 |
| `repository`    | string | Full repository name in `owner/repo-name` format                          |
| `message`       | string | Descriptive message about the update operation                            |
| `method_used`   | string | The update method that was successfully used (see Update Methods below)   |
| `error_details` | string | _(Error responses only)_ Additional details about the error that occurred |

**Update Methods:**

The service automatically tries different update strategies in order of preference:

| Method                  | Description                                                                                                                        |
| ----------------------- | ---------------------------------------------------------------------------------------------------------------------------------- |
| `fork`                  | **Preferred method.** Pulls latest changes from the upstream template repository. Only works if the repository is actually a fork. |
| `duplicate`             | **Fallback method.** Clones the original template, replaces the remote origin, and pushes to the target repository.                |
| `create-using-template` | **Last resort.** Clones both repositories, copies files from template to target (excluding .git), commits and pushes the changes.  |

**Error Responses:**

```json
{
    "status": "error",
    "slug": "awesome-time-tracking",
    "owner": "",
    "repository": "/awesome-time-tracking-website",
    "message": "Failed to update website repository",
    "error_details": "Directory with slug 'awesome-time-tracking' not found"
}
```

**Prerequisites:**

- The directory must exist (created via `/directories` endpoint)
- The website repository must exist (created via `/generate` endpoint)
- Valid GitHub authentication token in environment

### 9. Deploy to Vercel

To deploy the website repository to Vercel, send a POST request to `http://localhost:3001/deploy/{slug}/vercel`.
the `slug` parameter should match the directory slug used when creating the website repository.
**Request:**

```
POST /deploy/awesome-time-tracking/vercel
```

**URL Parameters:**

| Parameter | Type   | Required   | Description                                    |
| --------- | ------ | ---------- | ---------------------------------------------- |
| `slug`    | string | `required` | The slug of the directory/repository to deploy |

**Request Body:**

```json
// Optional:
{
    "GITHUB_TOKEN": "gh_sqjhqwghsydghsydfgsdyfgdsyf",
    "VERCEL_TOKEN": "e21qwyu2ewgfcuydesgf7udsdsfds"
}
```

> Request body is optional for now, by default it will take values from `.env` during development. Don't forget to change it before going to production, because it will save these tokens inside user's gh actions secrets...

> This endpoint will trigger GitHub Actions Workflow inside website repository. Important thing to note is that we cannot reuse `GITHUB_TOKEN` from github actions workflow because it has short lifetime while our website needs long living github token to make periodically clones, pulls etc.

### Example Prompt used to generate awesome time tracking in ever works org

```json
{
    "slug": "awesome-time-tracking",
    "name": "Awesome Time Tracking",
    "prompt": "Generate list of best time tracking software for business. Start with Open Source projects first, then prioritize Commercial solutions. Include both open-source and commercial solutions. You can check these URLs for reference: https://github.com/awesome-lists/awesome-time-tracking https://alternativeto.net/category/productivity/time-tracking/",
    "company": {
        "name": "Ever Co.",
        "website": "https://ever.co"
    },
    "target_keywords": [
        "time tracking",
        "productivity",
        "project management",
        "timesheet",
        "work hours"
    ],
    "initial_categories": ["Open Source", "Commercial"],
    "priority_categories": ["Enterprise"]
}
```

```

```
